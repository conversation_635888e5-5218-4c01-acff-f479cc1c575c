#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffd1060f8e7, pid=9860, tid=16540
#
# JRE version: Java(TM) SE Runtime Environment (21.0.4+8) (build 21.0.4+8-LTS-274)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.4+8-LTS-274, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
# V  [jvm.dll+0x67f8e7]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://bugreport.java.com/bugreport/crash.jsp
#

---------------  S U M M A R Y ------------

Command Line: -XX:TieredStopAtLevel=1 -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 org.technoserve.udp.UdpApplication

Host: Intel(R) Core(TM) i5-6300U CPU @ 2.40GHz, 4 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
Time: Thu May 29 12:29:35 2025 India Standard Time elapsed time: 17.362830 seconds (0d 0h 0m 17s)

---------------  T H R E A D  ---------------

Current thread (0x00000214f3860790):  WorkerThread "GC Thread#3"    [id=16540, stack(0x000000a6d9300000,0x000000a6d9400000) (1024K)]

Stack: [0x000000a6d9300000,0x000000a6d9400000],  sp=0x000000a6d93ff570,  free space=1021k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x67f8e7]
V  [jvm.dll+0x3bce58]
V  [jvm.dll+0x6d5d72]
V  [jvm.dll+0x356022]
V  [jvm.dll+0x880f17]
V  [jvm.dll+0x7fd65b]
V  [jvm.dll+0x6c753d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), reading address 0x0000200000000100


Registers:
RAX=0x0000000000000070, RBX=0x00000214adb99d50, RCX=0x0000200000000000, RDX=0x0000000000000000
RSP=0x000000a6d93ff570, RBP=0x00000214ae02f4b8, RSI=0x0000200000000000, RDI=0x0000000000000009
R8 =0x0000000000000003, R9 =0x0000000000000001, R10=0x0000021499420000, R11=0x000000a6d93ff4f0
R12=0x0000000000000030, R13=0x0000000000000000, R14=0x0000000000000000, R15=0x00000000000003d8
RIP=0x00007ffd1060f8e7, EFLAGS=0x0000000000010202


Register to memory mapping:

RAX=0x0000000000000070 is an unknown value
RBX=0x00000214adb99d50 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
RCX=0x0000200000000000 is an unknown value
RDX=0x0 is null
RSP=0x000000a6d93ff570 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
RBP=0x00000214ae02f4b8 is a pointer to class: 
java.lang.CharacterData {0x00000214ae02f4c0}
 - instance size:     2
 - klass size:        92
 - access:            synchronized abstract 
 - flags:             rewritten is_shared_boot_class has_localvariable_table has_vanilla_constructor has_final_method 
 - state:             fully_initialized
 - name:              'java/lang/CharacterData'
 - super:             'java/lang/Object'
 - sub:               'java/lang/CharacterData00'   'java/lang/CharacterDataLatin1'   
 - arrays:            null
 - methods:           Array<T>(0x00000214ae665f68)
 - method ordering:   Array<T>(0x00000214ae666190)
 - default_methods:   Array<T>(0x0000000000000000)
 - local interfaces:  Array<T>(0x00000214ae613a28)
 - trans. interfaces: Array<T>(0x00000214ae613a28)
 - constants:         constant pool [86] {0x00000214ae665608} for 'java/lang/CharacterData' cache=0x00000214ae419800
 - class loader data:  loader data: 0x00000214ada21960 of 'bootstrap'
 - source file:       'CharacterData.java'
 - class annotations:       Array<T>(0x0000000000000000)
 - class type annotations:  Array<T>(0x0000000000000000)
 - field annotations:       Array<T>(0x0000000000000000)
 - field type annotations:  Array<T>(0x0000000000000000)
 - inner classes:     Array<T>(0x00000214ae6139b0)
 - nest members:     Array<T>(0x00000214ae6139b0)
 - permitted subclasses:     Array<T>(0x00000214ae6139b0)
 - java mirror:       a 'java/lang/Class'{0x00000007025fd068} = 'java/lang/CharacterData'
 - vtable length      33  (start addr: 0x00000214ae02f680)
 - itable length      2 (start addr: 0x00000214ae02f788)
 - ---- static fields (0 words):
 - ---- non-static fields (0 words):
 - non-static oop maps: 
RSI=0x0000200000000000 is an unknown value
RDI=0x0000000000000009 is an unknown value
R8 =0x0000000000000003 is an unknown value
R9 =0x0000000000000001 is an unknown value
R10=0x0000021499420000 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
R11=0x000000a6d93ff4f0 points into unknown readable memory: 0x00000000000003d8 | d8 03 00 00 00 00 00 00
R12=0x0000000000000030 is an unknown value
R13=0x0 is null
R14=0x0 is null
R15=0x00000000000003d8 is an unknown value

Top of Stack: (sp=0x000000a6d93ff570)
0x000000a6d93ff570:   0000000000000000 0000000000000030
0x000000a6d93ff580:   0000000000000000 00007ffd1053a272
0x000000a6d93ff590:   00000214afd71c08 000000a600000000
0x000000a6d93ff5a0:   00000000000003d8 0000000000000048
0x000000a6d93ff5b0:   0000000000000000 0000000000000030
0x000000a6d93ff5c0:   0000000000000009 00007ffd1034ce58
0x000000a6d93ff5d0:   00000000000001fe 0000200000000000
0x000000a6d93ff5e0:   00000214ae02f4b8 00000214f30560d0
0x000000a6d93ff5f0:   00000214f30564b8 00000214f3470510
0x000000a6d93ff600:   00000214f30560d0 00000214f30560e0
0x000000a6d93ff610:   000000a6d83ff000 00007ffd10665d72
0x000000a6d93ff620:   000000a6d83ff000 00000214f3470510
0x000000a6d93ff630:   00000214f30564b8 0000000000000000
0x000000a6d93ff640:   0000000000000070 00000214f34766c8
0x000000a6d93ff650:   0000000000000002 00007ffd102e6022
0x000000a6d93ff660:   000000a6d83fefc0 00000214f3860790
0x000000a6d93ff670:   0000000000000002 0000000000000002
0x000000a6d93ff680:   00000214adb99d50 00007ffd10810f17
0x000000a6d93ff690:   000002149b5118d8 00000214f3860790
0x000000a6d93ff6a0:   0000000000000002 00000214adb99d50
0x000000a6d93ff6b0:   0000000000000000 00000214f3860790
0x000000a6d93ff6c0:   0000000000000000 0000000000100000
0x000000a6d93ff6d0:   0000000000000000 00007ffd1078d65b
0x000000a6d93ff6e0:   000000a6d9300000 00000000ffffffff
0x000000a6d93ff6f0:   0000000000000000 0000000000000000
0x000000a6d93ff700:   0000000000000004 0000000000001000
0x000000a6d93ff710:   0000000400001000 0000000000020000
0x000000a6d93ff720:   0000000000000000 00007ffd1078de2a
0x000000a6d93ff730:   00000214f3860790 0000000000000000
0x000000a6d93ff740:   00007b9eb5d87030 0000000000000000
0x000000a6d93ff750:   0000000000000000 00007ffd1065753d
0x000000a6d93ff760:   00000214f3860790 00000214f3860790 

Instructions: (pc=0x00007ffd1060f8e7)
0x00007ffd1060f7e7:   34 27 00 48 8b 7c 24 50 4c 8b 64 24 58 48 8b 5c
0x00007ffd1060f7f7:   24 60 48 8b 6c 24 68 48 83 c4 30 41 5f 41 5e 5e
0x00007ffd1060f807:   c3 48 8b cb e8 e0 8d ac ff 0f b6 c0 4c 8d 0d ae
0x00007ffd1060f817:   f6 39 00 41 b8 73 05 00 00 89 44 24 20 48 8d 15
0x00007ffd1060f827:   b5 f6 39 00 b9 00 00 00 e0 e8 6b da bf ff cc 48
0x00007ffd1060f837:   8b cb e8 b2 8d ac ff 0f b6 c0 4c 8d 0d 80 f6 39
0x00007ffd1060f847:   00 41 b8 f1 06 00 00 89 44 24 20 48 8d 15 87 f6
0x00007ffd1060f857:   39 00 b9 00 00 00 e0 e8 3d da bf ff cc 48 8b cb
0x00007ffd1060f867:   e8 84 8d ac ff 0f b6 c0 4c 8d 0d 52 f6 39 00 41
0x00007ffd1060f877:   b8 73 05 00 00 89 44 24 20 48 8d 15 59 f6 39 00
0x00007ffd1060f887:   b9 00 00 00 e0 e8 0f da bf ff cc cc cc cc cc cc
0x00007ffd1060f897:   cc cc cc cc cc cc cc cc cc 48 89 5c 24 10 48 89
0x00007ffd1060f8a7:   6c 24 18 48 89 74 24 20 57 41 54 41 55 41 56 41
0x00007ffd1060f8b7:   57 48 83 ec 30 65 48 8b 04 25 58 00 00 00 44 0f
0x00007ffd1060f8c7:   b6 f2 44 8b 05 d8 82 5e 00 48 8b f1 4a 8b 1c c0
0x00007ffd1060f8d7:   b8 70 00 00 00 80 3c 18 00 75 05 e8 49 20 27 00
0x00007ffd1060f8e7:   83 be 00 01 00 00 00 b8 20 00 00 00 48 8b 04 18
0x00007ffd1060f8f7:   48 8b a8 18 03 00 00 48 8b 7d 28 4c 8b 7d 10 4c
0x00007ffd1060f907:   8b 65 18 4c 8b 6d 20 48 89 7c 24 60 7e 65 48 8d
0x00007ffd1060f917:   8e 08 01 00 00 e8 7f 0d 00 00 48 8b f8 48 85 c0
0x00007ffd1060f927:   74 4c 0f 1f 80 00 00 00 00 48 8b 07 41 0f b6 d6
0x00007ffd1060f937:   48 8b cf ff 90 80 00 00 00 8b 5f 08 48 8b cf 48
0x00007ffd1060f947:   8b 07 2b de ff 10 8d 48 e0 8d 0c cb 3b 8e 00 01
0x00007ffd1060f957:   00 00 7d 1a 48 63 c9 48 81 c1 08 01 00 00 48 03
0x00007ffd1060f967:   ce e8 33 0d 00 00 48 8b f8 48 85 c0 75 bb 48 8b
0x00007ffd1060f977:   7c 24 60 48 63 86 04 01 00 00 83 f8 fe 74 35 48
0x00007ffd1060f987:   8d 88 08 01 00 00 48 03 ce e8 0b 0d 00 00 48 8b
0x00007ffd1060f997:   c8 48 8b d8 48 8b 10 ff 52 68 33 c9 84 c0 48 0f
0x00007ffd1060f9a7:   45 cb 48 85 c9 74 0d 48 8b 01 41 0f b6 d6 ff 90
0x00007ffd1060f9b7:   80 00 00 00 48 8d 05 3e f9 39 00 44 88 74 24 28
0x00007ffd1060f9c7:   48 8d 54 24 20 48 89 44 24 20 48 8b ce e8 87 fc
0x00007ffd1060f9d7:   ff ff 49 83 3f 00 74 13 48 8b d7 48 8b cd e8 f6 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x0 is null
stack at sp + 1 slots: 0x0000000000000030 is an unknown value
stack at sp + 2 slots: 0x0 is null
stack at sp + 3 slots: 0x00007ffd1053a272 jvm.dll
stack at sp + 4 slots: 0x00000214afd71c08 is a pointer to class: 
org.springframework.data.jpa.projection.CollectionAwareProjectionFactory$CollectionAwareProjectionInformation {0x00000214afd71c10}
 - instance size:     3
 - klass size:        76
 - access:            synchronized 
 - flags:             rewritten has_nonstatic_fields should_verify_class has_nonstatic_concrete_methods has_localvariable_table has_miranda_methods 
 - state:             fully_initialized
 - name:              'org/springframework/data/jpa/projection/CollectionAwareProjectionFactory$CollectionAwareProjectionInformation'
 - super:             'org/springframework/data/projection/SpelAwareProxyProjectionFactory$SpelAwareProjectionInformation'
 - sub:               
 - arrays:            null
 - methods:           Array<T>(0x00000214fa0a0dd8)
 - method ordering:   Array<T>(0x00000214fa1a2c20)
 - default_methods:   Array<T>(0x00000214fa1a2c30)
 - default vtable indices:   Array<T>(0x00000214fa1a2c40)
 - local interfaces:  Array<T>(0x00000214ae613a28)
 - trans. interfaces: Array<T>(0x00000214f9d61690)
 - constants:         constant pool [55] {0x00000214f99171f0} for 'org/springframework/data/jpa/projection/CollectionAwareProjectionFactory$CollectionAwareProjectionInformation' cache=0x00000214fa1a2d98
 - class loader data:  loader data: 0x00000214adbcdc50 for instance a 'jdk/internal/loader/ClassLoaders$AppClassLoader'{0x0000000702aa3338}
 - source file:       'CollectionAwareProjectionFactory.java'
 - class annotations:       Array<T>(0x0000000000000000)
 - class type annotations:  Array<T>(0x0000000000000000)
 - field annotations:       Array<T>(0x0000000000000000)
 - field type annotations:  Array<T>(0x0000000000000000)
 - inner classes:     Array<T>(0x00000214fa0d9e00)
 - nest members:     Array<T>(0x00000214ae6139b0)
 - permitted subclasses:     Array<T>(0x00000214ae6139b0)
 - java mirror:       a 'java/lang/Class'{0x0000000706e652b0} = 'org/springframework/data/jpa/projection/CollectionAwareProjectionFactory$CollectionAwareProjectionInformation'
 - vtable length      10  (start addr: 0x00000214afd71dd0)
 - itable length      8 (start addr: 0x00000214afd71e20)
 - ---- static fields (0 words):
 - ---- non-static fields (3 words):
 - private final 'projectionType' 'Ljava/lang/Class;' @12 
 - private final 'properties' 'Ljava/util/List;' @16 
 - private final 'inputProperties' 'Ljava/util/List;' @20 
 - non-static oop maps: 12-20 
stack at sp + 5 slots: 0x000000a600000000 is an unknown value
stack at sp + 6 slots: 0x00000000000003d8 is an unknown value
stack at sp + 7 slots: 0x0000000000000048 is an unknown value


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000214f8460650, length=31, elements={
0x000002149946df80, 0x00000214adb8e520, 0x00000214adb8f440, 0x00000214adb965e0,
0x00000214adb981b0, 0x00000214adb9ac20, 0x00000214adb9eb80, 0x00000214adb9f5e0,
0x00000214f316b090, 0x00000214f3176280, 0x00000214f317aaf0, 0x00000214f3180440,
0x00000214f3218e50, 0x00000214f3a3ff30, 0x00000214f3d7cd00, 0x00000214f3dc14b0,
0x00000214f44cbfd0, 0x00000214f5978e20, 0x00000214f593b420, 0x00000214f593be60,
0x00000214f59d10e0, 0x00000214f59e8190, 0x00000214f59e7b00, 0x00000214f59e4d10,
0x00000214f59e6750, 0x00000214f59e5a30, 0x00000214f59e53a0, 0x00000214f59e7470,
0x00000214f59e60c0, 0x00000214f73b7140, 0x00000214f73b84f0
}

Java Threads: ( => current thread )
  0x000002149946df80 JavaThread "main"                              [_thread_blocked, id=17512, stack(0x000000a6d7c00000,0x000000a6d7d00000) (1024K)]
  0x00000214adb8e520 JavaThread "Reference Handler"          daemon [_thread_blocked, id=17816, stack(0x000000a6d8500000,0x000000a6d8600000) (1024K)]
  0x00000214adb8f440 JavaThread "Finalizer"                  daemon [_thread_blocked, id=10136, stack(0x000000a6d8600000,0x000000a6d8700000) (1024K)]
  0x00000214adb965e0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=15508, stack(0x000000a6d8700000,0x000000a6d8800000) (1024K)]
  0x00000214adb981b0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=10388, stack(0x000000a6d8800000,0x000000a6d8900000) (1024K)]
  0x00000214adb9ac20 JavaThread "Service Thread"             daemon [_thread_blocked, id=17520, stack(0x000000a6d8900000,0x000000a6d8a00000) (1024K)]
  0x00000214adb9eb80 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=18392, stack(0x000000a6d8a00000,0x000000a6d8b00000) (1024K)]
  0x00000214adb9f5e0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=10868, stack(0x000000a6d8b00000,0x000000a6d8c00000) (1024K)]
  0x00000214f316b090 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_in_native, id=17944, stack(0x000000a6d8c00000,0x000000a6d8d00000) (1024K)]
  0x00000214f3176280 JavaThread "JDWP Event Helper Thread"   daemon [_thread_blocked, id=13488, stack(0x000000a6d8d00000,0x000000a6d8e00000) (1024K)]
  0x00000214f317aaf0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=18284, stack(0x000000a6d8e00000,0x000000a6d8f00000) (1024K)]
  0x00000214f3180440 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=15104, stack(0x000000a6d8f00000,0x000000a6d9000000) (1024K)]
  0x00000214f3218e50 JavaThread "C1 CompilerThread1"         daemon [_thread_blocked, id=18364, stack(0x000000a6d9000000,0x000000a6d9100000) (1024K)]
  0x00000214f3a3ff30 JavaThread "C1 CompilerThread2"         daemon [_thread_blocked, id=4400, stack(0x000000a6d9400000,0x000000a6d9500000) (1024K)]
  0x00000214f3d7cd00 JavaThread "Thread-0"                          [_thread_blocked, id=11712, stack(0x000000a6d9500000,0x000000a6d9600000) (1024K)]
  0x00000214f3dc14b0 JavaThread "restartedMain"                     [_thread_blocked, id=3632, stack(0x000000a6d9600000,0x000000a6d9700000) (1024K)]
  0x00000214f44cbfd0 JavaThread "Gax-1"                      daemon [_thread_blocked, id=14876, stack(0x000000a6d9700000,0x000000a6d9800000) (1024K)]
  0x00000214f5978e20 JavaThread "grpc-nio-worker-ELG-1-1"    daemon [_thread_in_native, id=7056, stack(0x000000a6d9800000,0x000000a6d9900000) (1024K)]
  0x00000214f593b420 JavaThread "grpc-default-executor-0"    daemon [_thread_blocked, id=10460, stack(0x000000a6d9900000,0x000000a6d9a00000) (1024K)]
  0x00000214f593be60 JavaThread "grpc-nio-worker-ELG-1-2"    daemon [_thread_in_native, id=4800, stack(0x000000a6d9a00000,0x000000a6d9b00000) (1024K)]
  0x00000214f59d10e0 JavaThread "Jndi-Dns-address-change-listener" daemon [_thread_in_native, id=15480, stack(0x000000a6d9b00000,0x000000a6d9c00000) (1024K)]
  0x00000214f59e8190 JavaThread "grpc-default-executor-1"    daemon [_thread_blocked, id=14752, stack(0x000000a6d9c00000,0x000000a6d9d00000) (1024K)]
  0x00000214f59e7b00 JavaThread "grpc-nio-worker-ELG-1-3"    daemon [_thread_in_native, id=6120, stack(0x000000a6d9d00000,0x000000a6d9e00000) (1024K)]
  0x00000214f59e4d10 JavaThread "grpc-default-executor-2"    daemon [_thread_blocked, id=17716, stack(0x000000a6d9e00000,0x000000a6d9f00000) (1024K)]
  0x00000214f59e6750 JavaThread "grpc-nio-worker-ELG-1-4"    daemon [_thread_in_native, id=16208, stack(0x000000a6d9f00000,0x000000a6da000000) (1024K)]
  0x00000214f59e5a30 JavaThread "Catalina-utility-1"                [_thread_blocked, id=10244, stack(0x000000a6da200000,0x000000a6da300000) (1024K)]
  0x00000214f59e53a0 JavaThread "Catalina-utility-2"                [_thread_blocked, id=4904, stack(0x000000a6da300000,0x000000a6da400000) (1024K)]
  0x00000214f59e7470 JavaThread "container-0"                       [_thread_blocked, id=15904, stack(0x000000a6da400000,0x000000a6da500000) (1024K)]
  0x00000214f59e60c0 JavaThread "PostgreSQL-JDBC-Cleaner"    daemon [_thread_blocked, id=4036, stack(0x000000a6da500000,0x000000a6da600000) (1024K)]
  0x00000214f73b7140 JavaThread "udp-pool housekeeper"       daemon [_thread_blocked, id=4728, stack(0x000000a6da600000,0x000000a6da700000) (1024K)]
  0x00000214f73b84f0 JavaThread "Gax-2"                      daemon [_thread_blocked, id=14532, stack(0x000000a6da800000,0x000000a6da900000) (1024K)]
Total: 31

Other Threads:
  0x00000214adb6ab80 VMThread "VM Thread"                           [id=6164, stack(0x000000a6d8300000,0x000000a6d8400000) (1024K)]
  0x00000214adb55be0 WatcherThread "VM Periodic Task Thread"        [id=7460, stack(0x000000a6d8200000,0x000000a6d8300000) (1024K)]
  0x000002149b5769a0 WorkerThread "GC Thread#0"                     [id=9612, stack(0x000000a6d7d00000,0x000000a6d7e00000) (1024K)]
  0x00000214f385fc40 WorkerThread "GC Thread#1"                     [id=15752, stack(0x000000a6d9100000,0x000000a6d9200000) (1024K)]
  0x00000214f385ffe0 WorkerThread "GC Thread#2"                     [id=11160, stack(0x000000a6d9200000,0x000000a6d9300000) (1024K)]
=>0x00000214f3860790 WorkerThread "GC Thread#3"                     [id=16540, stack(0x000000a6d9300000,0x000000a6d9400000) (1024K)]
  0x000002149b588110 ConcurrentGCThread "G1 Main Marker"            [id=16964, stack(0x000000a6d7e00000,0x000000a6d7f00000) (1024K)]
  0x000002149b5893b0 WorkerThread "G1 Conc#0"                       [id=16224, stack(0x000000a6d7f00000,0x000000a6d8000000) (1024K)]
  0x00000214ada241a0 ConcurrentGCThread "G1 Refine#0"               [id=14736, stack(0x000000a6d8000000,0x000000a6d8100000) (1024K)]
  0x000002149b505010 ConcurrentGCThread "G1 Service"                [id=13892, stack(0x000000a6d8100000,0x000000a6d8200000) (1024K)]
Total: 10

Threads with active compile tasks:
Total: 0

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffd10bc7e98] Threads_lock - owner thread: 0x00000214adb6ab80
[0x00007ffd10bc7f98] Heap_lock - owner thread: 0x000002149b588110

Heap address: 0x0000000702400000, size: 4060 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x00000214ae000000-0x00000214aec90000-0x00000214aec90000), size 13172736, SharedBaseAddress: 0x00000214ae000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000214af000000-0x00000214ef000000, reserved size: 1073741824
Narrow klass base: 0x00000214ae000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 4 total, 4 available
 Memory: 16239M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 4060M
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 151552K, used 77984K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 13 young (26624K), 3 survivors (6144K)
 Metaspace       used 96182K, committed 97024K, reserved 1179648K
  class space    used 13476K, committed 13824K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000702400000, 0x0000000702600000, 0x0000000702600000|100%| O|  |TAMS 0x0000000702600000| PB 0x0000000702400000| Untracked 
|   1|0x0000000702600000, 0x0000000702800000, 0x0000000702800000|100%| O|  |TAMS 0x0000000702800000| PB 0x0000000702600000| Untracked 
|   2|0x0000000702800000, 0x0000000702a00000, 0x0000000702a00000|100%| O|  |TAMS 0x0000000702a00000| PB 0x0000000702800000| Untracked 
|   3|0x0000000702a00000, 0x0000000702c00000, 0x0000000702c00000|100%| O|  |TAMS 0x0000000702c00000| PB 0x0000000702a00000| Untracked 
|   4|0x0000000702c00000, 0x0000000702e00000, 0x0000000702e00000|100%| O|  |TAMS 0x0000000702e00000| PB 0x0000000702c00000| Untracked 
|   5|0x0000000702e00000, 0x0000000702fd2ae8, 0x0000000703000000| 91%| O|  |TAMS 0x0000000702fd2ae8| PB 0x0000000702e00000| Untracked 
|   6|0x0000000703000000, 0x0000000703200000, 0x0000000703200000|100%| O|  |TAMS 0x0000000703200000| PB 0x0000000703000000| Untracked 
|   7|0x0000000703200000, 0x0000000703400000, 0x0000000703400000|100%| O|  |TAMS 0x0000000703400000| PB 0x0000000703200000| Untracked 
|   8|0x0000000703400000, 0x0000000703600000, 0x0000000703600000|100%| O|  |TAMS 0x0000000703600000| PB 0x0000000703400000| Untracked 
|   9|0x0000000703600000, 0x0000000703800000, 0x0000000703800000|100%| O|  |TAMS 0x0000000703800000| PB 0x0000000703600000| Untracked 
|  10|0x0000000703800000, 0x0000000703a00000, 0x0000000703a00000|100%| O|  |TAMS 0x0000000703a00000| PB 0x0000000703800000| Untracked 
|  11|0x0000000703a00000, 0x0000000703c00000, 0x0000000703c00000|100%| O|  |TAMS 0x0000000703c00000| PB 0x0000000703a00000| Untracked 
|  12|0x0000000703c00000, 0x0000000703e00000, 0x0000000703e00000|100%| O|  |TAMS 0x0000000703e00000| PB 0x0000000703c00000| Untracked 
|  13|0x0000000703e00000, 0x0000000704000000, 0x0000000704000000|100%| O|  |TAMS 0x0000000704000000| PB 0x0000000703e00000| Untracked 
|  14|0x0000000704000000, 0x0000000704000000, 0x0000000704200000|  0%| F|  |TAMS 0x0000000704000000| PB 0x0000000704000000| Untracked 
|  15|0x0000000704200000, 0x0000000704400000, 0x0000000704400000|100%| O|  |TAMS 0x0000000704400000| PB 0x0000000704200000| Untracked 
|  16|0x0000000704400000, 0x0000000704600000, 0x0000000704600000|100%| O|  |TAMS 0x0000000704600000| PB 0x0000000704400000| Untracked 
|  17|0x0000000704600000, 0x0000000704800000, 0x0000000704800000|100%| O|  |TAMS 0x0000000704800000| PB 0x0000000704600000| Untracked 
|  18|0x0000000704800000, 0x0000000704a00000, 0x0000000704a00000|100%| O|  |TAMS 0x0000000704a00000| PB 0x0000000704800000| Untracked 
|  19|0x0000000704a00000, 0x0000000704c00000, 0x0000000704c00000|100%| O|  |TAMS 0x0000000704c00000| PB 0x0000000704a00000| Untracked 
|  20|0x0000000704c00000, 0x0000000704e00000, 0x0000000704e00000|100%| O|  |TAMS 0x0000000704e00000| PB 0x0000000704c00000| Untracked 
|  21|0x0000000704e00000, 0x0000000705000000, 0x0000000705000000|100%| O|  |TAMS 0x0000000705000000| PB 0x0000000704e00000| Untracked 
|  22|0x0000000705000000, 0x0000000705200000, 0x0000000705200000|100%| O|  |TAMS 0x0000000705200000| PB 0x0000000705000000| Untracked 
|  23|0x0000000705200000, 0x0000000705400000, 0x0000000705400000|100%| O|  |TAMS 0x0000000705400000| PB 0x0000000705200000| Untracked 
|  24|0x0000000705400000, 0x0000000705600000, 0x0000000705600000|100%| O|  |TAMS 0x0000000705600000| PB 0x0000000705400000| Untracked 
|  25|0x0000000705600000, 0x0000000705800000, 0x0000000705800000|100%| O|  |TAMS 0x0000000705800000| PB 0x0000000705600000| Untracked 
|  26|0x0000000705800000, 0x0000000705a00000, 0x0000000705a00000|100%| O|  |TAMS 0x0000000705a00000| PB 0x0000000705800000| Untracked 
|  27|0x0000000705a00000, 0x0000000705c00000, 0x0000000705c00000|100%| O|  |TAMS 0x0000000705c00000| PB 0x0000000705a00000| Untracked 
|  28|0x0000000705c00000, 0x0000000705c29a60, 0x0000000705e00000|  8%| O|  |TAMS 0x0000000705c29a60| PB 0x0000000705c00000| Untracked 
|  29|0x0000000705e00000, 0x0000000705e00000, 0x0000000706000000|  0%| F|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Untracked 
|  30|0x0000000706000000, 0x0000000706000000, 0x0000000706200000|  0%| F|  |TAMS 0x0000000706000000| PB 0x0000000706000000| Untracked 
|  31|0x0000000706200000, 0x0000000706200000, 0x0000000706400000|  0%| F|  |TAMS 0x0000000706200000| PB 0x0000000706200000| Untracked 
|  32|0x0000000706400000, 0x0000000706400000, 0x0000000706600000|  0%| F|  |TAMS 0x0000000706400000| PB 0x0000000706400000| Untracked 
|  33|0x0000000706600000, 0x000000070662bdf8, 0x0000000706800000|  8%| S|CS|TAMS 0x0000000706600000| PB 0x0000000706600000| Complete 
|  34|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000| PB 0x0000000706800000| Untracked 
|  35|0x0000000706a00000, 0x0000000706a00000, 0x0000000706c00000|  0%| F|  |TAMS 0x0000000706a00000| PB 0x0000000706a00000| Untracked 
|  36|0x0000000706c00000, 0x0000000706e00000, 0x0000000706e00000|100%| S|CS|TAMS 0x0000000706c00000| PB 0x0000000706c00000| Complete 
|  37|0x0000000706e00000, 0x0000000707000000, 0x0000000707000000|100%| S|CS|TAMS 0x0000000706e00000| PB 0x0000000706e00000| Complete 
|  38|0x0000000707000000, 0x0000000707000000, 0x0000000707200000|  0%| F|  |TAMS 0x0000000707000000| PB 0x0000000707000000| Untracked 
|  39|0x0000000707200000, 0x0000000707200000, 0x0000000707400000|  0%| F|  |TAMS 0x0000000707200000| PB 0x0000000707200000| Untracked 
|  40|0x0000000707400000, 0x0000000707400000, 0x0000000707600000|  0%| F|  |TAMS 0x0000000707400000| PB 0x0000000707400000| Untracked 
|  41|0x0000000707600000, 0x0000000707600000, 0x0000000707800000|  0%| F|  |TAMS 0x0000000707600000| PB 0x0000000707600000| Untracked 
|  42|0x0000000707800000, 0x0000000707800000, 0x0000000707a00000|  0%| F|  |TAMS 0x0000000707800000| PB 0x0000000707800000| Untracked 
|  43|0x0000000707a00000, 0x0000000707a00000, 0x0000000707c00000|  0%| F|  |TAMS 0x0000000707a00000| PB 0x0000000707a00000| Untracked 
|  44|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000| PB 0x0000000707c00000| Untracked 
|  45|0x0000000707e00000, 0x0000000707e00000, 0x0000000708000000|  0%| F|  |TAMS 0x0000000707e00000| PB 0x0000000707e00000| Untracked 
|  46|0x0000000708000000, 0x0000000708000000, 0x0000000708200000|  0%| F|  |TAMS 0x0000000708000000| PB 0x0000000708000000| Untracked 
|  47|0x0000000708200000, 0x0000000708200000, 0x0000000708400000|  0%| F|  |TAMS 0x0000000708200000| PB 0x0000000708200000| Untracked 
|  48|0x0000000708400000, 0x0000000708400000, 0x0000000708600000|  0%| F|  |TAMS 0x0000000708400000| PB 0x0000000708400000| Untracked 
|  49|0x0000000708600000, 0x0000000708600000, 0x0000000708800000|  0%| F|  |TAMS 0x0000000708600000| PB 0x0000000708600000| Untracked 
|  50|0x0000000708800000, 0x0000000708800000, 0x0000000708a00000|  0%| F|  |TAMS 0x0000000708800000| PB 0x0000000708800000| Untracked 
|  51|0x0000000708a00000, 0x0000000708a00000, 0x0000000708c00000|  0%| F|  |TAMS 0x0000000708a00000| PB 0x0000000708a00000| Untracked 
|  52|0x0000000708c00000, 0x0000000708c00000, 0x0000000708e00000|  0%| F|  |TAMS 0x0000000708c00000| PB 0x0000000708c00000| Untracked 
|  53|0x0000000708e00000, 0x0000000708e00000, 0x0000000709000000|  0%| F|  |TAMS 0x0000000708e00000| PB 0x0000000708e00000| Untracked 
|  54|0x0000000709000000, 0x0000000709000000, 0x0000000709200000|  0%| F|  |TAMS 0x0000000709000000| PB 0x0000000709000000| Untracked 
|  55|0x0000000709200000, 0x0000000709200000, 0x0000000709400000|  0%| F|  |TAMS 0x0000000709200000| PB 0x0000000709200000| Untracked 
|  56|0x0000000709400000, 0x0000000709400000, 0x0000000709600000|  0%| F|  |TAMS 0x0000000709400000| PB 0x0000000709400000| Untracked 
|  57|0x0000000709600000, 0x0000000709600000, 0x0000000709800000|  0%| F|  |TAMS 0x0000000709600000| PB 0x0000000709600000| Untracked 
|  58|0x0000000709800000, 0x0000000709800000, 0x0000000709a00000|  0%| F|  |TAMS 0x0000000709800000| PB 0x0000000709800000| Untracked 
|  59|0x0000000709a00000, 0x0000000709a00000, 0x0000000709c00000|  0%| F|  |TAMS 0x0000000709a00000| PB 0x0000000709a00000| Untracked 
|  60|0x0000000709c00000, 0x0000000709c00000, 0x0000000709e00000|  0%| F|  |TAMS 0x0000000709c00000| PB 0x0000000709c00000| Untracked 
|  61|0x0000000709e00000, 0x0000000709e00000, 0x000000070a000000|  0%| F|  |TAMS 0x0000000709e00000| PB 0x0000000709e00000| Untracked 
|  62|0x000000070a000000, 0x000000070a000000, 0x000000070a200000|  0%| F|  |TAMS 0x000000070a000000| PB 0x000000070a000000| Untracked 
|  63|0x000000070a200000, 0x000000070a200000, 0x000000070a400000|  0%| F|  |TAMS 0x000000070a200000| PB 0x000000070a200000| Untracked 
|  64|0x000000070a400000, 0x000000070a500800, 0x000000070a600000| 50%| E|  |TAMS 0x000000070a400000| PB 0x000000070a400000| Complete 
|  65|0x000000070a600000, 0x000000070a800000, 0x000000070a800000|100%| E|CS|TAMS 0x000000070a600000| PB 0x000000070a600000| Complete 
|  66|0x000000070a800000, 0x000000070aa00000, 0x000000070aa00000|100%| E|CS|TAMS 0x000000070a800000| PB 0x000000070a800000| Complete 
|  67|0x000000070aa00000, 0x000000070ac00000, 0x000000070ac00000|100%| E|CS|TAMS 0x000000070aa00000| PB 0x000000070aa00000| Complete 
|  68|0x000000070ac00000, 0x000000070ae00000, 0x000000070ae00000|100%| E|CS|TAMS 0x000000070ac00000| PB 0x000000070ac00000| Complete 
|  69|0x000000070ae00000, 0x000000070b000000, 0x000000070b000000|100%| E|CS|TAMS 0x000000070ae00000| PB 0x000000070ae00000| Complete 
| 114|0x0000000710800000, 0x0000000710a00000, 0x0000000710a00000|100%| E|CS|TAMS 0x0000000710800000| PB 0x0000000710800000| Complete 
| 115|0x0000000710a00000, 0x0000000710c00000, 0x0000000710c00000|100%| E|CS|TAMS 0x0000000710a00000| PB 0x0000000710a00000| Complete 
| 116|0x0000000710c00000, 0x0000000710e00000, 0x0000000710e00000|100%| E|CS|TAMS 0x0000000710c00000| PB 0x0000000710c00000| Complete 
| 126|0x0000000712000000, 0x0000000712200000, 0x0000000712200000|100%| E|CS|TAMS 0x0000000712000000| PB 0x0000000712000000| Complete 

Card table byte_map: [0x00000214a7160000,0x00000214a7950000] _byte_map_base: 0x00000214a394e000

Marking Bits: (CMBitMap*) 0x000002149b5778b0
 Bits: [0x00000214a7950000, 0x00000214ab8c0000)

Polling page: 0x00000214993a0000

Metaspace:

Usage:
  Non-class:     80.77 MB used.
      Class:     13.16 MB used.
       Both:     93.93 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      81.25 MB ( 63%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      13.50 MB (  1%) committed,  1 nodes.
             Both:        1.12 GB reserved,      94.75 MB (  8%) committed. 

Chunk freelists:
   Non-Class:  14.05 MB
       Class:  2.50 MB
        Both:  16.55 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 157.19 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 18.
num_arena_births: 1146.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1516.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 18.
num_chunks_taken_from_freelist: 4568.
num_chunk_merges: 12.
num_chunk_splits: 3369.
num_chunks_enlarged: 2661.
num_inconsistent_stats: 0.

CodeCache: size=49152Kb used=17976Kb max_used=18181Kb free=31175Kb
 bounds [0x00000214a3970000, 0x00000214a4b40000, 0x00000214a6970000]
 total_blobs=9996 nmethods=9167 adapters=750
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 17.312 Thread 0x00000214adb9f5e0 10076       1       org.springframework.context.expression.StandardBeanExpressionResolver$$Lambda/0x00000214af3b8768::apply (12 bytes)
Event: 17.312 Thread 0x00000214f3a3ff30 10077       1       org.springframework.context.expression.StandardBeanExpressionResolver::lambda$evaluate$0 (15 bytes)
Event: 17.312 Thread 0x00000214f3218e50 10078       1       org.springframework.expression.common.TemplateAwareExpressionParser::parseExpression (39 bytes)
Event: 17.312 Thread 0x00000214f3a3ff30 nmethod 10077 0x00000214a3fe1690 code [0x00000214a3fe1840, 0x00000214a3fe1988]
Event: 17.312 Thread 0x00000214adb9f5e0 nmethod 10076 0x00000214a3ff0d10 code [0x00000214a3ff0ec0, 0x00000214a3ff1060]
Event: 17.312 Thread 0x00000214f3a3ff30 10079       1       org.springframework.expression.common.TemplateAwareExpressionParser::parseTemplate (44 bytes)
Event: 17.312 Thread 0x00000214adb9f5e0 10080       1       org.springframework.expression.common.TemplateAwareExpressionParser::parseExpressions (287 bytes)
Event: 17.313 Thread 0x00000214f3a3ff30 nmethod 10079 0x00000214a3fab710 code [0x00000214a3fab8e0, 0x00000214a3fabbe8]
Event: 17.313 Thread 0x00000214f3218e50 nmethod 10078 0x00000214a4165a10 code [0x00000214a4165c20, 0x00000214a4166068]
Event: 17.313 Thread 0x00000214f3a3ff30 10081       1       org.springframework.context.expression.StandardBeanExpressionResolver$1::getExpressionPrefix (8 bytes)
Event: 17.313 Thread 0x00000214f3218e50 10082       1       org.springframework.context.expression.StandardBeanExpressionResolver$1::getExpressionSuffix (8 bytes)
Event: 17.313 Thread 0x00000214f3a3ff30 nmethod 10081 0x00000214a3ffbd10 code [0x00000214a3ffbea0, 0x00000214a3ffbf78]
Event: 17.313 Thread 0x00000214f3218e50 nmethod 10082 0x00000214a3ffe510 code [0x00000214a3ffe6a0, 0x00000214a3ffe778]
Event: 17.313 Thread 0x00000214f3a3ff30 10083       1       org.springframework.expression.common.LiteralExpression::<init> (10 bytes)
Event: 17.313 Thread 0x00000214f3a3ff30 nmethod 10083 0x00000214a4042290 code [0x00000214a4042420, 0x00000214a4042538]
Event: 17.315 Thread 0x00000214adb9f5e0 nmethod 10080 0x00000214a46bf810 code [0x00000214a46bfb80, 0x00000214a46c0ce0]
Event: 17.320 Thread 0x00000214adb9f5e0 10084       1       org.springframework.boot.web.servlet.context.WebApplicationContextServletContextAwareProcessor::getServletConfig (23 bytes)
Event: 17.320 Thread 0x00000214adb9f5e0 nmethod 10084 0x00000214a4044890 code [0x00000214a4044a20, 0x00000214a4044b38]
Event: 17.326 Thread 0x00000214f3a3ff30 10085       1       sun.reflect.generics.reflectiveObjects.WildcardTypeImpl::equals (52 bytes)
Event: 17.327 Thread 0x00000214f3a3ff30 nmethod 10085 0x00000214a3fa3910 code [0x00000214a3fa3b40, 0x00000214a3fa41b8]

GC Heap History (20 events):
Event: 12.471 GC heap before
{Heap before GC invocations=19 (full 0):
 garbage-first heap   total 102400K, used 91391K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 26 young (53248K), 2 survivors (4096K)
 Metaspace       used 79171K, committed 79872K, reserved 1179648K
  class space    used 10810K, committed 11072K, reserved 1048576K
}
Event: 12.478 GC heap after
{Heap after GC invocations=20 (full 0):
 garbage-first heap   total 102400K, used 42781K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 79171K, committed 79872K, reserved 1179648K
  class space    used 10810K, committed 11072K, reserved 1048576K
}
Event: 13.019 GC heap before
{Heap before GC invocations=20 (full 0):
 garbage-first heap   total 102400K, used 87837K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 25 young (51200K), 3 survivors (6144K)
 Metaspace       used 81616K, committed 82304K, reserved 1179648K
  class space    used 11165K, committed 11456K, reserved 1048576K
}
Event: 13.028 GC heap after
{Heap after GC invocations=21 (full 0):
 garbage-first heap   total 102400K, used 44556K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 81616K, committed 82304K, reserved 1179648K
  class space    used 11165K, committed 11456K, reserved 1048576K
}
Event: 13.477 GC heap before
{Heap before GC invocations=21 (full 0):
 garbage-first heap   total 102400K, used 62988K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 12 young (24576K), 2 survivors (4096K)
 Metaspace       used 83876K, committed 84608K, reserved 1179648K
  class space    used 11500K, committed 11840K, reserved 1048576K
}
Event: 13.487 GC heap after
{Heap after GC invocations=22 (full 0):
 garbage-first heap   total 102400K, used 47340K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 83876K, committed 84608K, reserved 1179648K
  class space    used 11500K, committed 11840K, reserved 1048576K
}
Event: 14.163 GC heap before
{Heap before GC invocations=23 (full 0):
 garbage-first heap   total 102400K, used 88300K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 23 young (47104K), 3 survivors (6144K)
 Metaspace       used 88101K, committed 88960K, reserved 1179648K
  class space    used 12281K, committed 12672K, reserved 1048576K
}
Event: 14.175 GC heap after
{Heap after GC invocations=24 (full 0):
 garbage-first heap   total 102400K, used 49971K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 88101K, committed 88960K, reserved 1179648K
  class space    used 12281K, committed 12672K, reserved 1048576K
}
Event: 14.602 GC heap before
{Heap before GC invocations=24 (full 0):
 garbage-first heap   total 102400K, used 88883K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 22 young (45056K), 3 survivors (6144K)
 Metaspace       used 92102K, committed 92928K, reserved 1179648K
  class space    used 12939K, committed 13312K, reserved 1048576K
}
Event: 14.615 GC heap after
{Heap after GC invocations=25 (full 0):
 garbage-first heap   total 122880K, used 51495K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 92102K, committed 92928K, reserved 1179648K
  class space    used 12939K, committed 13312K, reserved 1048576K
}
Event: 16.086 GC heap before
{Heap before GC invocations=25 (full 0):
 garbage-first heap   total 122880K, used 106791K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 30 young (61440K), 3 survivors (6144K)
 Metaspace       used 95650K, committed 96512K, reserved 1179648K
  class space    used 13394K, committed 13760K, reserved 1048576K
}
Event: 16.096 GC heap after
{Heap after GC invocations=26 (full 0):
 garbage-first heap   total 122880K, used 54337K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 95650K, committed 96512K, reserved 1179648K
  class space    used 13394K, committed 13760K, reserved 1048576K
}
Event: 16.344 GC heap before
{Heap before GC invocations=26 (full 0):
 garbage-first heap   total 122880K, used 105537K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 29 young (59392K), 4 survivors (8192K)
 Metaspace       used 95766K, committed 96576K, reserved 1179648K
  class space    used 13407K, committed 13760K, reserved 1048576K
}
Event: 16.358 GC heap after
{Heap after GC invocations=27 (full 0):
 garbage-first heap   total 122880K, used 58655K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 95766K, committed 96576K, reserved 1179648K
  class space    used 13407K, committed 13760K, reserved 1048576K
}
Event: 16.525 GC heap before
{Heap before GC invocations=28 (full 0):
 garbage-first heap   total 151552K, used 99615K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 25 young (51200K), 4 survivors (8192K)
 Metaspace       used 95766K, committed 96576K, reserved 1179648K
  class space    used 13407K, committed 13760K, reserved 1048576K
}
Event: 16.539 GC heap after
{Heap after GC invocations=29 (full 0):
 garbage-first heap   total 151552K, used 62382K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 95766K, committed 96576K, reserved 1179648K
  class space    used 13407K, committed 13760K, reserved 1048576K
}
Event: 16.963 GC heap before
{Heap before GC invocations=29 (full 0):
 garbage-first heap   total 151552K, used 132014K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 38 young (77824K), 4 survivors (8192K)
 Metaspace       used 96134K, committed 96960K, reserved 1179648K
  class space    used 13468K, committed 13824K, reserved 1048576K
}
Event: 16.972 GC heap after
{Heap after GC invocations=30 (full 0):
 garbage-first heap   total 151552K, used 58366K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 96134K, committed 96960K, reserved 1179648K
  class space    used 13468K, committed 13824K, reserved 1048576K
}
Event: 17.196 GC heap before
{Heap before GC invocations=30 (full 0):
 garbage-first heap   total 151552K, used 132094K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 38 young (77824K), 2 survivors (4096K)
 Metaspace       used 96136K, committed 96960K, reserved 1179648K
  class space    used 13468K, committed 13824K, reserved 1048576K
}
Event: 17.203 GC heap after
{Heap after GC invocations=31 (full 0):
 garbage-first heap   total 151552K, used 59552K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 96136K, committed 96960K, reserved 1179648K
  class space    used 13468K, committed 13824K, reserved 1048576K
}

Dll operation events (11 events):
Event: 0.012 Loaded shared library C:\Program Files\Java\jdk-21\bin\java.dll
Event: 0.218 Loaded shared library C:\Program Files\Java\jdk-21\bin\net.dll
Event: 0.219 Loaded shared library C:\Program Files\Java\jdk-21\bin\nio.dll
Event: 0.223 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.506 Loaded shared library C:\Program Files\Java\jdk-21\bin\jimage.dll
Event: 0.860 Loaded shared library C:\Program Files\Java\jdk-21\bin\management.dll
Event: 0.863 Loaded shared library C:\Program Files\Java\jdk-21\bin\management_ext.dll
Event: 1.818 Loaded shared library C:\Program Files\Java\jdk-21\bin\sunmscapi.dll
Event: 1.883 Loaded shared library C:\Program Files\Java\jdk-21\bin\extnet.dll
Event: 2.002 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\io_grpc_netty_shaded_netty_tcnative_windows_x86_6412758314857119541152.dll
Event: 7.990 Loaded shared library C:\Program Files\Java\jdk-21\bin\verify.dll

Deoptimization events (20 events):
Event: 16.956 Thread 0x00000214f3dc14b0 DEOPT PACKING pc=0x00000214a4390a4c sp=0x000000a6d96fc010
Event: 16.956 Thread 0x00000214f3dc14b0 DEOPT UNPACKING pc=0x00000214a39c4e42 sp=0x000000a6d96fb4e0 mode 1
Event: 16.956 Thread 0x00000214f3dc14b0 DEOPT PACKING pc=0x00000214a46abad4 sp=0x000000a6d96fc180
Event: 16.956 Thread 0x00000214f3dc14b0 DEOPT UNPACKING pc=0x00000214a39c4e42 sp=0x000000a6d96fb658 mode 1
Event: 16.987 Thread 0x00000214f3dc14b0 DEOPT PACKING pc=0x00000214a4390a4c sp=0x000000a6d96fc040
Event: 16.987 Thread 0x00000214f3dc14b0 DEOPT UNPACKING pc=0x00000214a39c4e42 sp=0x000000a6d96fb510 mode 1
Event: 16.987 Thread 0x00000214f3dc14b0 DEOPT PACKING pc=0x00000214a46abad4 sp=0x000000a6d96fc1b0
Event: 16.987 Thread 0x00000214f3dc14b0 DEOPT UNPACKING pc=0x00000214a39c4e42 sp=0x000000a6d96fb688 mode 1
Event: 16.989 Thread 0x00000214f3dc14b0 DEOPT PACKING pc=0x00000214a4390a4c sp=0x000000a6d96fc040
Event: 16.989 Thread 0x00000214f3dc14b0 DEOPT UNPACKING pc=0x00000214a39c4e42 sp=0x000000a6d96fb510 mode 1
Event: 16.989 Thread 0x00000214f3dc14b0 DEOPT PACKING pc=0x00000214a46abad4 sp=0x000000a6d96fc1b0
Event: 16.989 Thread 0x00000214f3dc14b0 DEOPT UNPACKING pc=0x00000214a39c4e42 sp=0x000000a6d96fb688 mode 1
Event: 16.991 Thread 0x00000214f3dc14b0 DEOPT PACKING pc=0x00000214a4390a4c sp=0x000000a6d96fc010
Event: 16.991 Thread 0x00000214f3dc14b0 DEOPT UNPACKING pc=0x00000214a39c4e42 sp=0x000000a6d96fb4e0 mode 1
Event: 16.991 Thread 0x00000214f3dc14b0 DEOPT PACKING pc=0x00000214a46abad4 sp=0x000000a6d96fc180
Event: 16.991 Thread 0x00000214f3dc14b0 DEOPT UNPACKING pc=0x00000214a39c4e42 sp=0x000000a6d96fb658 mode 1
Event: 17.329 Thread 0x00000214f3dc14b0 DEOPT PACKING pc=0x00000214a4390a4c sp=0x000000a6d96fc040
Event: 17.329 Thread 0x00000214f3dc14b0 DEOPT UNPACKING pc=0x00000214a39c4e42 sp=0x000000a6d96fb510 mode 1
Event: 17.329 Thread 0x00000214f3dc14b0 DEOPT PACKING pc=0x00000214a46abad4 sp=0x000000a6d96fc1b0
Event: 17.329 Thread 0x00000214f3dc14b0 DEOPT UNPACKING pc=0x00000214a39c4e42 sp=0x000000a6d96fb688 mode 1

Classes loaded (20 events):
Event: 15.908 Loading class java/net/FileNameMap done
Event: 15.908 Loading class java/net/URLConnection$1 done
Event: 15.908 Loading class sun/net/www/MimeTable
Event: 15.909 Loading class sun/net/www/MimeTable done
Event: 15.909 Loading class sun/net/www/MimeTable$1
Event: 15.909 Loading class sun/net/www/MimeTable$1 done
Event: 15.909 Loading class sun/net/www/MimeTable$2
Event: 15.909 Loading class sun/net/www/MimeTable$2 done
Event: 15.909 Loading class sun/net/www/MimeTable$DefaultInstanceHolder
Event: 15.909 Loading class sun/net/www/MimeTable$DefaultInstanceHolder done
Event: 15.909 Loading class sun/net/www/MimeTable$DefaultInstanceHolder$1
Event: 15.909 Loading class sun/net/www/MimeTable$DefaultInstanceHolder$1 done
Event: 15.909 Loading class sun/net/www/MimeEntry
Event: 15.910 Loading class sun/net/www/MimeEntry done
Event: 16.838 Loading class java/util/stream/Streams$RangeIntSpliterator
Event: 16.838 Loading class java/util/stream/Streams$RangeIntSpliterator done
Event: 16.839 Loading class java/util/stream/IntPipeline$1
Event: 16.839 Loading class java/util/stream/IntPipeline$1 done
Event: 16.840 Loading class java/util/stream/IntPipeline$1$1
Event: 16.840 Loading class java/util/stream/IntPipeline$1$1 done

Classes unloaded (1 events):
Event: 17.330 Thread 0x00000214adb6ab80 Unloading class 0x00000214afd24000 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor27'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 12.965 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000705c2ec88}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x0000000705c2ec88) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 12.971 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000705c61070}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000705c61070) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 12.971 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000705c65908}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000705c65908) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 12.971 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000705c69de0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000705c69de0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 12.988 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x0000000705cfb5c8}: Found class java.lang.Object, but interface was expected> (0x0000000705cfb5c8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 13.063 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000007121967a8}: Found class java.lang.Object, but interface was expected> (0x00000007121967a8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 13.075 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000710c12278}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000710c12278) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 13.082 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000710c5b080}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000710c5b080) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 13.082 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000710c5f5e0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000710c5f5e0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 13.083 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000710c635f8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x0000000710c635f8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 13.198 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000710bdde30}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x0000000710bdde30) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 13.454 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000707473378}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000707473378) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 13.550 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x0000000710d04c20}: Found class java.lang.Object, but interface was expected> (0x0000000710d04c20) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 13.618 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x0000000710ba0570}: Found class java.lang.Object, but interface was expected> (0x0000000710ba0570) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 13.814 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000007078c49d8}: Found class java.lang.Object, but interface was expected> (0x00000007078c49d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 14.571 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000070631bf38}: Found class java.lang.Object, but interface was expected> (0x000000070631bf38) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 14.909 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000707ebfe60}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000707ebfe60) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 14.918 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000707f3ed88}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000707f3ed88) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 14.985 Thread 0x00000214f3dc14b0 Exception <a 'java/net/UnknownHostException'{0x0000000707d5d740}> (0x0000000707d5d740) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 16.839 Thread 0x00000214f3dc14b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000707dc6ae0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x0000000707dc6ae0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 14.615 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 15.629 Executing VM operation: Cleanup
Event: 15.629 Executing VM operation: Cleanup done
Event: 16.086 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 16.096 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 16.344 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 16.358 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 16.454 Executing VM operation: G1PauseRemark
Event: 16.462 Executing VM operation: G1PauseRemark done
Event: 16.504 Executing VM operation: G1PauseCleanup
Event: 16.504 Executing VM operation: G1PauseCleanup done
Event: 16.525 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 16.539 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 16.963 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 16.972 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 17.196 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 17.203 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 17.290 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 17.290 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 17.330 Executing VM operation: G1PauseRemark

Events (20 events):
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a41dce10
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a41dd790
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a41ffc10
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a4200a10
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a4200f90
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a4202010
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a4202310
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a4202610
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a4202910
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a44b5f10
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a44b6210
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a44b9d90
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a44baa90
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a44d2210
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a44d2a90
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a44d7190
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a452ff90
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a46a7d10
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a46bf810
Event: 16.461 Thread 0x00000214adb6ab80 flushing nmethod 0x00000214a46e3910


Dynamic libraries:
0x00007ff652ab0000 - 0x00007ff652ac0000 	C:\Program Files\Java\jdk-21\bin\java.exe
0x00007ffdbcb10000 - 0x00007ffdbcd08000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffdbb010000 - 0x00007ffdbb0d2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffdba720000 - 0x00007ffdbaa16000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffdba4f0000 - 0x00007ffdba5f0000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffdb6ba0000 - 0x00007ffdb6bb9000 	C:\Program Files\Java\jdk-21\bin\jli.dll
0x00007ffdbac10000 - 0x00007ffdbacbf000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffdbca30000 - 0x00007ffdbcace000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffdbad20000 - 0x00007ffdbadbf000 	C:\WINDOWS\System32\sechost.dll
0x00007ffdbc270000 - 0x00007ffdbc393000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffdba4c0000 - 0x00007ffdba4e7000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffdbbf20000 - 0x00007ffdbc0bd000 	C:\WINDOWS\System32\USER32.dll
0x00007ffdba3e0000 - 0x00007ffdba402000 	C:\WINDOWS\System32\win32u.dll
0x00007ffdbc490000 - 0x00007ffdbc4bb000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffdbaa20000 - 0x00007ffdbab3a000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffdba680000 - 0x00007ffdba71d000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffdb6400000 - 0x00007ffdb641b000 	C:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007ffdb95e0000 - 0x00007ffdb987a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c\COMCTL32.dll
0x00007ffdb5ff0000 - 0x00007ffdb5ffa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffdbc4c0000 - 0x00007ffdbc4ef000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffdb7020000 - 0x00007ffdb702c000 	C:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007ffd9c6f0000 - 0x00007ffd9c77e000 	C:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007ffd0ff90000 - 0x00007ffd10ca3000 	C:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007ffdbb6e0000 - 0x00007ffdbb74b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffdba010000 - 0x00007ffdba05b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffda53a0000 - 0x00007ffda53c7000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffdb9ff0000 - 0x00007ffdba002000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffdb82f0000 - 0x00007ffdb8302000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffdb6f60000 - 0x00007ffdb6f6a000 	C:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007ffdb9a90000 - 0x00007ffdb9c91000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffdb9a50000 - 0x00007ffdb9a84000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffdba5f0000 - 0x00007ffdba672000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffd9d860000 - 0x00007ffd9d89c000 	C:\Program Files\Java\jdk-21\bin\jdwp.dll
0x00007ffdb63e0000 - 0x00007ffdb63ff000 	C:\Program Files\Java\jdk-21\bin\java.dll
0x00007ffdbb750000 - 0x00007ffdbbebe000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffdb7110000 - 0x00007ffdb78b3000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffdbc550000 - 0x00007ffdbc8a3000 	C:\WINDOWS\System32\combase.dll
0x00007ffdb9370000 - 0x00007ffdb939b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ffdbc8b0000 - 0x00007ffdbc97d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffdbab60000 - 0x00007ffdbac0d000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffdbacc0000 - 0x00007ffdbad15000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffdba0e0000 - 0x00007ffdba105000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffdb6b30000 - 0x00007ffdb6b3c000 	C:\Program Files\Java\jdk-21\bin\dt_socket.dll
0x00007ffdb8e50000 - 0x00007ffdb8e8b000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffdb9150000 - 0x00007ffdb91bc000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffdb6ef0000 - 0x00007ffdb6f00000 	C:\Program Files\Java\jdk-21\bin\net.dll
0x00007ffdae530000 - 0x00007ffdae63a000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffdb63c0000 - 0x00007ffdb63d6000 	C:\Program Files\Java\jdk-21\bin\nio.dll
0x00007ffdb2ca0000 - 0x00007ffdb2cb8000 	C:\Program Files\Java\jdk-21\bin\zip.dll
0x00007ffdb6aa0000 - 0x00007ffdb6aaa000 	C:\Program Files\Java\jdk-21\bin\management.dll
0x00007ffdb63b0000 - 0x00007ffdb63bb000 	C:\Program Files\Java\jdk-21\bin\management_ext.dll
0x00007ffdbb6d0000 - 0x00007ffdbb6d8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffdb37a0000 - 0x00007ffdb37ae000 	C:\Program Files\Java\jdk-21\bin\sunmscapi.dll
0x00007ffdba280000 - 0x00007ffdba3dd000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffdb94c0000 - 0x00007ffdb94e7000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffdb9480000 - 0x00007ffdb94bb000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffdb5fc0000 - 0x00007ffdb5fc7000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffdb9e50000 - 0x00007ffdb9e68000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffdb8af0000 - 0x00007ffdb8b28000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffdba060000 - 0x00007ffdba08e000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffdb92e0000 - 0x00007ffdb92ec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffdbc420000 - 0x00007ffdbc428000 	C:\WINDOWS\System32\NSI.dll
0x00007ffdb36d0000 - 0x00007ffdb36d9000 	C:\Program Files\Java\jdk-21\bin\extnet.dll
0x00007ffd74100000 - 0x00007ffd743c3000 	C:\Users\<USER>\AppData\Local\Temp\io_grpc_netty_shaded_netty_tcnative_windows_x86_6412758314857119541152.dll
0x00007ffdb8e90000 - 0x00007ffdb8f5a000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffdb0970000 - 0x00007ffdb097a000 	C:\Windows\System32\rasadhlp.dll
0x00007ffdae0f0000 - 0x00007ffdae107000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffdaf130000 - 0x00007ffdaf14d000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffda69d0000 - 0x00007ffda6a50000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffdb6b90000 - 0x00007ffdb6ba0000 	C:\Program Files\Java\jdk-21\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c;C:\Program Files\Java\jdk-21\bin\server;C:\Users\<USER>\AppData\Local\Temp

VM Arguments:
jvm_args: -XX:TieredStopAtLevel=1 -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 
java_command: org.technoserve.udp.UdpApplication
java_class_path (initial): D:\TECHNOSERVE\technoserve\Backend\target\classes;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.5.16\logback-classic-1.5.16.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.5.16\logback-core-1.5.16.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.24.3\log4j-to-slf4j-2.24.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\2.0.16\jul-to-slf4j-2.0.16.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.3\snakeyaml-2.3.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\5.1.0\HikariCP-5.1.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\6.2.2\spring-jdbc-6.2.2.jar;C:\Users\<USER>\.m2\repository\org\hibernate\orm\hibernate-core\6.6.5.Final\hibernate-core-6.6.5.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\3.1.0\jakarta.persistence-api-3.1.0.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\2.0.1\jakarta.transaction-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.6.1.Final\jboss-logging-3.6.1.Final.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\7.0.3.Final\hibernate-commons-annotations-7.0.3.Final.jar;C:\Users\<USER>\.m2\repository\io\smallrye\jandex\3.2.0\jandex-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.15.11\byte-buddy-1.15.11.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\4.0.5\jaxb-runtime-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;C:\Users\<USER>\.m2\repository\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\org\antlr\antlr4-runtime\4.13.0\antlr4-runtime-4.13.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\3.4.2\spring-data-jpa-3.4.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\3.4.2\spring-data-commons-3.4.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\6.2.2\spring-orm-6.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.2.2\spring-context-6.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\6.2.2\spring-aop-6.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.2.2\spring-tx-6.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.2.2\spring-beans-6.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\6.2.2\spring-aspects-6.2.2.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.22.1\aspectjweaver-1.9.22.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\6.4.2\spring-security-config-6.4.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\6.4.2\spring-security-core-6.4.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\6.4.2\spring-security-crypto-6.4.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.2.2\spring-expression-6.2.2.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.14.3\micrometer-observation-1.14.3.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.14.3\micrometer-commons-1.14.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-oauth2-client\6.4.2\spring-security-oauth2-client-6.4.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-oauth2-core\6.4.2\spring-security-oauth2-core-6.4.2.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\oauth2-oidc-sdk\9.43.4\oauth2-oidc-sdk-9.43.4.jar;C:\Users\<USER>\.m2\repository\com\github\stephenc\jcip\jcip-annotations\1.0-1\jcip-annotations-1.0-1.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\content-type\2.2\content-type-2.2.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\lang-tag\1.7\lang-tag-1.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-oauth2-jose\6.4.2\spring-security-oauth2-jose-6.4.2.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\nimbus-jose-jwt\9.37.3\nimbus-jose-jwt-9.37.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-oauth2-resource-server\6.4.2\spring-security-oauth2-resource-server-6.4.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.18.2\jackson-datatype-jdk8-2.18.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.18.2\jackson-module-parameter-names-2.18.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.2.2\spring-web-6.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\6.2.2\spring-webmvc-6.2.2.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.34\tomcat-embed-el-10.1.34.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\8.0.2.Final\hibernate-validator-8.0.2.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-devtools\3.4.2\spring-boot-devtools-3.4.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.4.2\spring-boot-3.4.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.4.2\spring-boot-autoconfigure-3.4.2.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.7.2\postgresql-42.7.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.42.0\checker-qual-3.42.0.jar;C:\Users\<USER>\.m2\repository\org\modelmapper\modelmapper\3.2.2\modelmapper-3.2.2.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\spring-cloud-gcp-starter\6.1.1\spring-cloud-gcp-starter-6.1.1.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\spring-cloud-gcp-core\6.1.1\spring-cloud-gcp-core-6.1.1.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\spring-cloud-gcp-autoconfigure\6.1.1\spring-cloud-gcp-autoconfigure-6.1.1.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\spring-cloud-gcp-secretmanager\6.1.1\spring-cloud-gcp-secretmanager-6.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-context\4.2.0\spring-cloud-context-4.2.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.17.0\commons-lang3-3.17.0.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-secretmanager\2.60.0\google-cloud-secretmanager-2.60.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-api\1.70.0\grpc-api-1.70.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.36.0\error_prone_annotations-2.36.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-stub\1.70.0\grpc-stub-1.70.0.jar;C:\Users\<USER>\.m2\repository\org\codehaus\mojo\animal-sniffer-annotations\1.24\animal-sniffer-annotations-1.24.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf\1.70.0\grpc-protobuf-1.70.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf-lite\1.70.0\grpc-protobuf-lite-1.70.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\api-common\2.46.1\api-common-2.46.1.jar;C:\Users\<USER>\.m2\repository\com\google\auto\value\auto-value-annotations\1.11.0\auto-value-annotations-1.11.0.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\3.0.0\j2objc-annotations-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.25.5\protobuf-java-3.25.5.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-common-protos\2.54.1\proto-google-common-protos-2.54.1.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-cloud-secretmanager-v1beta1\2.60.0\proto-google-cloud-secretmanager-v1beta1-2.60.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-cloud-secretmanager-v1beta2\2.60.0\proto-google-cloud-secretmanager-v1beta2-2.60.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-iam-v1\1.49.1\proto-google-iam-v1-1.49.1.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-cloud-secretmanager-v1\2.60.0\proto-google-cloud-secretmanager-v1-2.60.0.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\33.3.1-jre\guava-33.3.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.2\failureaccess-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\api\gax\2.63.1\gax-2.63.1.jar;C:\Users\<USER>\.m2\repository\com\google\auth\google-auth-library-credentials\1.33.1\google-auth-library-credentials-1.33.1.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java-util\3.25.5\protobuf-java-util-3.25.5.jar;C:\Users\<USER>\.m2\repository\io\opencensus\opencensus-api\0.31.1\opencensus-api-0.31.1.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-context\1.70.0\grpc-context-1.70.0.jar;C:\Users\<USER>\.m2\repository\com\google\auth\google-auth-library-oauth2-http\1.33.1\google-auth-library-oauth2-http-1.33.1.jar;C:\Users\<USER>\.m2\repository\com\google\api\gax-grpc\2.63.1\gax-grpc-2.63.1.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-inprocess\1.70.0\grpc-inprocess-1.70.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-core\1.70.0\grpc-core-1.70.0.jar;C:\Users\<USER>\.m2\repository\com\google\android\annotations\4.1.1.4\annotations-4.1.1.4.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-alts\1.70.0\grpc-alts-1.70.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-grpclb\1.70.0\grpc-grpclb-1.70.0.jar;C:\Users\<USER>\.m2\repository\org\conscrypt\conscrypt-openjdk-uber\2.5.2\conscrypt-openjdk-uber-2.5.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-auth\1.70.0\grpc-auth-1.70.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-netty-shaded\1.70.0\grpc-netty-shaded-1.70.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-util\1.70.0\grpc-util-1.70.0.jar;C:\Users\<USER>\.m2\repository\io\perfmark\perfmark-api\0.27.0\perfmark-api-0.27.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-googleapis\1.70.0\grpc-googleapis-1.70.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-xds\1.70.0\grpc-xds-1.70.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-services\1.70.0\grpc-services-1.70.0.jar;C:\Users\<USER>\.m2\repository\com\google\re2j\re2j\1.7\re2j-1.7.jar;C:\Users\<USER>\.m2\repository\com\google\api\gax-httpjson\2.63.1\gax-httpjson-2.63.1.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.11.0\gson-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client\1.46.3\google-http-client-1.46.3.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.17.2\commons-codec-1.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\io\opencensus\opencensus-contrib-http-util\0.31.1\opencensus-contrib-http-util-0.31.1.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client-gson\1.46.3\google-http-client-gson-1.46.3.jar;C:\Users\<USER>\.m2\repository\org\threeten\threetenbp\1.7.0\threetenbp-1.7.0.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-storage\2.49.0\google-cloud-storage-2.49.0.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client-jackson2\1.46.3\google-http-client-jackson2-1.46.3.jar;C:\Users\<USER>\.m2\repository\com\google\api-client\google-api-client\2.7.2\google-api-client-2.7.2.jar;C:\Users\<USER>\.m2\repository\com\google\oauth-client\google-oauth-client\1.37.0\google-oauth-client-1.37.0.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client-apache-v2\1.46.3\google-http-client-apache-v2-1.46.3.jar;C:\Users\<USER>\.m2\repository\com\google\apis\google-api-services-storage\v1-rev20241206-2.0.0\google-api-services-storage-v1-rev20241206-2.0.0.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-core\2.52.0\google-cloud-core-2.52.0.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-core-http\2.52.0\google-cloud-core-http-2.52.0.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client-appengine\1.46.3\google-http-client-appengine-1.46.3.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-core-grpc\2.52.0\google-cloud-core-grpc-2.52.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-context\1.43.0\opentelemetry-context-1.43.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-cloud-storage-v2\2.49.0\proto-google-cloud-storage-v2-2.49.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\grpc-google-cloud-storage-v2\2.49.0\grpc-google-cloud-storage-v2-2.49.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\gapic-google-cloud-storage-v2\2.49.0\gapic-google-cloud-storage-v2-2.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk\1.43.0\opentelemetry-sdk-1.43.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-trace\1.43.0\opentelemetry-sdk-trace-1.43.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-api-incubator\1.43.0-alpha\opentelemetry-api-incubator-1.43.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-logs\1.43.0\opentelemetry-sdk-logs-1.43.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-opentelemetry\1.70.0\grpc-opentelemetry-1.70.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-api\1.43.0\opentelemetry-api-1.43.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-metrics\1.43.0\opentelemetry-sdk-metrics-1.43.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-common\1.43.0\opentelemetry-sdk-common-1.43.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-extension-autoconfigure-spi\1.43.0\opentelemetry-sdk-extension-autoconfigure-spi-1.43.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\semconv\opentelemetry-semconv\1.27.0-alpha\opentelemetry-semconv-1.27.0-alpha.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\opentelemetry\exporter-metrics\0.33.0\exporter-metrics-0.33.0.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-monitoring\3.52.0\google-cloud-monitoring-3.52.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-cloud-monitoring-v3\3.52.0\proto-google-cloud-monitoring-v3-3.52.0.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\opentelemetry\shared-resourcemapping\0.33.0\shared-resourcemapping-0.33.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\contrib\opentelemetry-gcp-resources\1.37.0-alpha\opentelemetry-gcp-resources-1.37.0-alpha.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\opentelemetry\detector-resources-support\0.33.0\detector-resources-support-0.33.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.18.2\jackson-core-2.18.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-rls\1.70.0\grpc-rls-1.70.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.0\jackson-datatype-jsr310-2.15.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.18.2\jackson-annotations-2.18.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.18.2\jackson-databind-2.18.2.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\5.4.1\poi-ooxml-5.4.1.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\5.4.1\poi-5.4.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\SparseBitSet\1.3\SparseBitSet-1.3.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-lite\5.4.1\poi-ooxml-lite-5.4.1.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\5.3.0\xmlbeans-5.3.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.27.1\commons-compress-1.27.1.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.18.0\commons-io-2.18.0.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.08\curvesapi-1.08.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.34\tomcat-embed-core-10.1.34.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.34\tomcat-embed-websocket-10.1.34.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.5.1\json-smart-2.5.1.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.5.1\accessors-smart-2.5.1.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.6\asm-9.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.2.2\spring-core-6.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.2.2\spring-jcl-6.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\6.4.2\spring-security-web-6.4.2.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4257218560                                {product} {ergonomic}
   size_t MaxNewSize                               = 2554331136                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 4096                                   {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4257218560                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-21
PATH=C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\TortoiseGit\bin;C:\Program Files\nodejs\;C:\nginx\;;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\PuTTY\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Java\jdk-21\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2024.2.1\bin;;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.2.2\bin;;C:\Program Files\Git\bin;C:\apache-maven-3.9.9\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\PostgreSQL\16\bin;C:\Users\<USER>\AppData\Local\Google\Cloud SDK\google-cloud-sdk\bin;C:\Users\<USER>\AppData\Local\Google\Cloud SDK\google-cloud-sdk\bin;;C:\Users\<USER>\AppData\Local\Programs\Ollama
USERNAME=PS-L-120
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 78 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
OS uptime: 0 days 2:02 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 78 stepping 3 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for all 4 processors :
  Max Mhz: 2501, Current Mhz: 2501, Mhz Limit: 2501

Memory: 4k page, system-wide physical 16239M (3790M free)
TotalPageFile size 26991M (AvailPageFile size 11951M)
current process WorkingSet (physical memory assigned to process): 374M, peak: 374M
current process commit charge ("private bytes"): 410M, peak: 410M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.4+8-LTS-274) for windows-amd64 JRE (21.0.4+8-LTS-274), built on 2024-06-05T05:23:33Z by "mach5one" with MS VC++ 17.6 (VS2022)

END.
