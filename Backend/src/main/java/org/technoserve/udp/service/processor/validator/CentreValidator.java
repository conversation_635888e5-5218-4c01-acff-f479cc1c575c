package org.technoserve.udp.service.processor.validator;

import org.technoserve.udp.entity.dataflow.Centre;
import org.technoserve.udp.repository.CentreTypeRepository;
import org.technoserve.udp.util.UdpCommonUtil;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Validator for Centre entities
 */
public class CentreValidator extends AbstractEntityValidator<Centre> {

  private static final Set<String> REQUIRED_FIELDS = new HashSet<>();

  private final CentreTypeRepository centreTypeRepository;

  public CentreValidator(CentreTypeRepository centreTypeRepository) {
    this.centreTypeRepository = centreTypeRepository;
  }

  static {
    REQUIRED_FIELDS.add("centreId");
    REQUIRED_FIELDS.add("centreName");
    REQUIRED_FIELDS.add("centreType");
    REQUIRED_FIELDS.add("facilitatorName");
    REQUIRED_FIELDS.add("facilitatorCountryCode");
    REQUIRED_FIELDS.add("facilitatorMobileNumber");
    REQUIRED_FIELDS.add("state");
    REQUIRED_FIELDS.add("district");
    REQUIRED_FIELDS.add("taluk");
    REQUIRED_FIELDS.add("village");
    REQUIRED_FIELDS.add("licenseCertificationType");
    REQUIRED_FIELDS.add("licenseCertificationStatus");
  }

  @Override
  public void validate(Centre centre, Map<String, String> validationErrors) {
    // Basic field validations
    if (centre.getCentreId() == null || centre.getCentreId().isEmpty()) {
      validationErrors.put("Centre ID", "Centre ID is required");
    }

    if (centre.getCentreName() == null || centre.getCentreName().isEmpty()) {
      validationErrors.put("Centre Name", "Centre Name is required");
    }

    if (centre.getCentreType() == null || centre.getCentreType().getType() == null) {
      validationErrors.put("Centre Type", "Centre Type is required");
    }

    if (centre.getFacilitatorName() == null || centre.getFacilitatorName().isEmpty()) {
      validationErrors.put("Facilitator Name", "Facilitator Name is required");
    }

    if (centre.getFacilitatorCountryCode() == null || centre.getFacilitatorCountryCode().isEmpty()) {
      validationErrors.put("Facilitator Country Code", "Facilitator Country Code is required");
    }

    if (centre.getFacilitatorMobileNumber() == null || centre.getFacilitatorMobileNumber().isEmpty()) {
      validationErrors.put("Facilitator Mobile Number", "Facilitator Mobile Number is required");
    }

    if (centre.getState() == null || centre.getState().isEmpty()) {
      validationErrors.put("State", "State is required");
    }

    if (centre.getDistrict() == null || centre.getDistrict().isEmpty()) {
      validationErrors.put("District", "District is required");
    }

    if (centre.getTaluk() == null || centre.getTaluk().isEmpty()) {
      validationErrors.put("Taluk", "Taluk is required");
    }

    if (centre.getVillage() == null || centre.getVillage().isEmpty()) {
      validationErrors.put("Village", "Village is required");
    }

    if (centre.getLicenseCertificationType() == null || centre.getLicenseCertificationType().isEmpty()) {
      validationErrors.put("License/Certification Type", "License/Certification Type is required");
    }

    if (centre.getLicenseCertificationStatus() == null || centre.getLicenseCertificationStatus().isEmpty()) {
      validationErrors.put("License/Certification Status", "License/Certification Status is required");
    }
  }

  @Override
  public void performCustomValidations(Centre centre, Map<String, String> validationErrors) {

    if (centre.getFacilitatorMobileNumber() != null && !centre.getFacilitatorMobileNumber().isEmpty() && !UdpCommonUtil.isValidLong(centre.getFacilitatorMobileNumber())) {
      validationErrors.put("Facilitator Country Code", "Facilitator Country Code is not valid");
    }

    // Validate mobile number format
    if (centre.getFacilitatorMobileNumber() != null && !centre.getFacilitatorMobileNumber().isEmpty() && !UdpCommonUtil.isValidLong(centre.getFacilitatorMobileNumber())) {
      validationErrors.put("Facilitator Mobile Number", "Facilitator Mobile Number is not valid");
    }

    // Centre Type validation is already handled in the validate method

    if (centre.getLatitude() != null && (centre.getLatitude() < -90 || centre.getLatitude() > 90)) {
      validationErrors.put("Lat", "Latitude must be between -90 and 90");
    }
    if (centre.getCentreType() != null && centre.getCentreType().getType() != null && !isValidCentreType(centre.getCentreType().getType())) {
      validationErrors.put("Centre Type", "Invalid Centre Type: " + centre.getCentreType() + ". Centre Type must exist in the database.");
    }
    if (centre.getLongitude() != null && (centre.getLongitude() < -180 || centre.getLongitude() > 180)) {
      validationErrors.put("Long", "Longitude must be between -180 and 180");
    }

  }

  @Override
  public boolean isRequiredField(String fieldName) {
    return REQUIRED_FIELDS.contains(fieldName);
  }

  @Override
  public Set<String> getRequiredFields() {
    return REQUIRED_FIELDS;
  }

  /**
   * Check if a centre type is valid
   *
   * @param centreType The centre type to check
   * @return True if the centre type is valid, false otherwise
   */
  public boolean isValidCentreType(String centreType) {
    if (centreType == null || centreType.isEmpty()) {
      return false;
    }
    return centreTypeRepository.existsByType(centreType.trim().toUpperCase());
  }
}
